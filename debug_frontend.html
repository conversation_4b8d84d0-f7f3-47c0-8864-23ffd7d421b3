<!DOCTYPE html>
<html>
<head>
    <title>Frontend Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Frontend Debug Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Direct API Call</h2>
        <button onclick="testDirectAPI()">Test API Call</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: State Update Logic</h2>
        <button onclick="testStateLogic()">Test State Update</button>
        <div id="state-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: hasMinimumParams Logic</h2>
        <button onclick="testMinimumParams()">Test Minimum Params</button>
        <div id="params-result"></div>
    </div>

    <script>
        async function testDirectAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p class="info">Testing API call...</p>';
            
            try {
                const response = await fetch('http://localhost:8002/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: "I need a wire shelf that is 36 inches wide, 18 inches long, 36 inches tall with 2 shelves",
                        session_id: "debug_test_" + Date.now()
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="success">✅ API Call Successful</h3>
                    <p><strong>AI Response:</strong> ${data.response.substring(0, 100)}...</p>
                    <p><strong>Has Sufficient:</strong> ${data.has_sufficient_entities}</p>
                    <p><strong>Extracted Entities:</strong></p>
                    <pre>${JSON.stringify(data.extracted_entities, null, 2)}</pre>
                `;
                
                // Test the exact logic from React app
                const entities = data.extracted_entities;
                const hasMinimum = entities.width && entities.length && entities.postHeight && entities.numberOfShelves;
                
                resultDiv.innerHTML += `
                    <p><strong>Frontend Logic Test:</strong></p>
                    <p>width: ${entities.width} (${typeof entities.width}) - ${entities.width ? '✅' : '❌'}</p>
                    <p>length: ${entities.length} (${typeof entities.length}) - ${entities.length ? '✅' : '❌'}</p>
                    <p>postHeight: ${entities.postHeight} (${typeof entities.postHeight}) - ${entities.postHeight ? '✅' : '❌'}</p>
                    <p>numberOfShelves: ${entities.numberOfShelves} (${typeof entities.numberOfShelves}) - ${entities.numberOfShelves ? '✅' : '❌'}</p>
                    <p><strong>hasMinimumParams Result: ${hasMinimum ? '✅ TRUE' : '❌ FALSE'}</strong></p>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }
        
        function testStateLogic() {
            const resultDiv = document.getElementById('state-result');
            
            // Simulate the React state update logic
            const initialState = {
                width: 0,
                length: 0,
                postHeight: 0,
                numberOfShelves: 0,
                accessories: {}
            };
            
            const newParams = {
                width: 36,
                length: 18,
                postHeight: 36,
                numberOfShelves: 2,
                accessories: {}
            };
            
            // Simulate the updateShelfParams logic
            const updatedParams = {
                ...initialState,
                ...newParams,
                accessories: newParams.accessories || initialState.accessories
            };
            
            const hasMinimum = updatedParams.width && updatedParams.length && updatedParams.postHeight && updatedParams.numberOfShelves;
            
            resultDiv.innerHTML = `
                <h3>State Update Simulation</h3>
                <p><strong>Initial State:</strong></p>
                <pre>${JSON.stringify(initialState, null, 2)}</pre>
                <p><strong>New Params:</strong></p>
                <pre>${JSON.stringify(newParams, null, 2)}</pre>
                <p><strong>Updated State:</strong></p>
                <pre>${JSON.stringify(updatedParams, null, 2)}</pre>
                <p><strong>hasMinimumParams: ${hasMinimum ? '✅ TRUE' : '❌ FALSE'}</strong></p>
            `;
        }
        
        function testMinimumParams() {
            const resultDiv = document.getElementById('params-result');
            
            const testCases = [
                { width: 0, length: 0, postHeight: 0, numberOfShelves: 0 },
                { width: 36, length: 0, postHeight: 0, numberOfShelves: 0 },
                { width: 36, length: 18, postHeight: 0, numberOfShelves: 0 },
                { width: 36, length: 18, postHeight: 36, numberOfShelves: 0 },
                { width: 36, length: 18, postHeight: 36, numberOfShelves: 2 },
            ];
            
            let html = '<h3>hasMinimumParams Test Cases</h3>';
            
            testCases.forEach((params, index) => {
                const hasMinimum = params.width && params.length && params.postHeight && params.numberOfShelves;
                html += `
                    <p><strong>Test ${index + 1}:</strong> 
                    W:${params.width} L:${params.length} H:${params.postHeight} S:${params.numberOfShelves} 
                    → ${hasMinimum ? '✅ TRUE' : '❌ FALSE'}</p>
                `;
            });
            
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
