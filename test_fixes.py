#!/usr/bin/env python3
"""
Test script to verify the fixes for frontend loading and 3D model rendering issues.
"""

import requests
import json
import time
import sys

def test_backend_health():
    """Test if backend is responding"""
    try:
        response = requests.get("http://localhost:8002/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check: PASSED")
            return True
        else:
            print(f"❌ Backend health check: FAILED (status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Backend health check: FAILED (error: {e})")
        return False

def test_chat_api():
    """Test chat API with parameters that should trigger 3D rendering"""
    try:
        # Test with complete parameters
        test_message = "I need a 36 inch wide, 18 inch deep, 72 inch tall shelf with 4 levels"
        
        response = requests.post(
            "http://localhost:8002/api/chat",
            json={
                "message": test_message,
                "session_id": "test_session_123"
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Chat API: PASSED")
            print(f"   Response received: {len(data.get('response', ''))} characters")
            
            # Check if entities were extracted
            entities = data.get('extracted_entities', {})
            has_sufficient = data.get('has_sufficient_entities', False)
            
            print(f"   Extracted entities: {entities}")
            print(f"   Has sufficient entities: {has_sufficient}")
            
            # Check for required parameters
            required_params = ['width', 'length', 'postHeight', 'numberOfShelves']
            missing_params = [param for param in required_params if param not in entities]
            
            if not missing_params:
                print("✅ Entity extraction: PASSED - All required parameters extracted")
                return True
            else:
                print(f"⚠️  Entity extraction: PARTIAL - Missing: {missing_params}")
                return False
        else:
            print(f"❌ Chat API: FAILED (status: {response.status_code})")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat API: FAILED (error: {e})")
        return False

def test_frontend_accessibility():
    """Test if frontend is accessible"""
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200:
            print("✅ Frontend accessibility: PASSED")
            print(f"   Page size: {len(response.content)} bytes")
            
            # Check for key elements
            content = response.text
            if "Wire Shelf Designer" in content:
                print("✅ Frontend content: PASSED - Key elements found")
                return True
            else:
                print("⚠️  Frontend content: PARTIAL - Some elements missing")
                return False
        else:
            print(f"❌ Frontend accessibility: FAILED (status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Frontend accessibility: FAILED (error: {e})")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Shelf-AI Fixes")
    print("=" * 50)
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Frontend Accessibility", test_frontend_accessibility),
        ("Chat API & Entity Extraction", test_chat_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The fixes appear to be working correctly.")
        print("\n📝 Next steps:")
        print("   1. Open http://localhost:3000 in your browser")
        print("   2. Try chatting with the AI to specify shelf dimensions")
        print("   3. Verify that the 3D model renders when sufficient parameters are provided")
        print("   4. Check that the loading performance is improved")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the issues above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
