#!/usr/bin/env python3

import requests
import json
import uuid
import time

def test_react_state_issue():
    """Test to identify the React state issue"""
    base_url = "http://localhost:8002/api"
    session_id = f"react_debug_{uuid.uuid4()}"
    
    print("🔍 COMPREHENSIVE REACT STATE DEBUG TEST")
    print("="*60)
    
    # Test 1: Verify backend is working
    print("\n1️⃣ TESTING BACKEND API")
    message = "I need a wire shelf that is 36 inches wide, 18 inches long, 36 inches tall with 2 shelves"
    
    try:
        response = requests.post(f"{base_url}/chat", json={
            "message": message,
            "session_id": session_id
        })
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend API working correctly")
            print(f"   - Has sufficient entities: {data['has_sufficient_entities']}")
            print(f"   - Extracted entities: {data['extracted_entities']}")
            
            # Test 2: Verify the exact logic React uses
            print("\n2️⃣ TESTING REACT LOGIC SIMULATION")
            entities = data['extracted_entities']
            
            # Simulate React's hasMinimumParams logic
            has_minimum = entities.get('width') and entities.get('length') and entities.get('postHeight') and entities.get('numberOfShelves')
            print(f"   - width: {entities.get('width')} → {bool(entities.get('width'))}")
            print(f"   - length: {entities.get('length')} → {bool(entities.get('length'))}")
            print(f"   - postHeight: {entities.get('postHeight')} → {bool(entities.get('postHeight'))}")
            print(f"   - numberOfShelves: {entities.get('numberOfShelves')} → {bool(entities.get('numberOfShelves'))}")
            print(f"   - hasMinimumParams result: {has_minimum}")
            
            if has_minimum:
                print("✅ React logic should show 3D model")
            else:
                print("❌ React logic will NOT show 3D model")
                
            # Test 3: Check for potential issues
            print("\n3️⃣ CHECKING FOR POTENTIAL ISSUES")
            
            # Check data types
            print("   Data type check:")
            for key in ['width', 'length', 'postHeight', 'numberOfShelves']:
                value = entities.get(key)
                print(f"   - {key}: {value} (type: {type(value).__name__})")
                
            # Check for zero values
            zero_values = [key for key, value in entities.items() if value == 0]
            if zero_values:
                print(f"   ⚠️  Zero values found: {zero_values}")
            else:
                print("   ✅ No zero values")
                
            # Check for None/null values
            none_values = [key for key, value in entities.items() if value is None]
            if none_values:
                print(f"   ⚠️  None values found: {none_values}")
            else:
                print("   ✅ No None values")
                
        else:
            print(f"❌ Backend API error: {response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ Backend API exception: {e}")
        return
    
    # Test 4: Recommendations
    print("\n4️⃣ DIAGNOSIS & RECOMMENDATIONS")
    
    if data['has_sufficient_entities'] and has_minimum:
        print("✅ Backend is providing correct data")
        print("✅ Logic simulation shows 3D should render")
        print("\n🔍 LIKELY ISSUES IN REACT APP:")
        print("   1. State update not triggering re-render")
        print("   2. Component not receiving updated props")
        print("   3. 3D component has rendering errors")
        print("   4. CSS/styling hiding the 3D component")
        print("\n🛠️  DEBUGGING STEPS:")
        print("   1. Open React app at http://localhost:3000")
        print("   2. Open browser console (F12)")
        print("   3. Send the test message in chat")
        print("   4. Look for debug logs showing state updates")
        print("   5. Click the 'FORCE TEST 3D' button to bypass chat")
        print("   6. Check for any JavaScript errors")
        
    else:
        print("❌ Backend or logic issue detected")
        print("   - Check backend response format")
        print("   - Verify entity extraction logic")

if __name__ == "__main__":
    test_react_state_issue()
